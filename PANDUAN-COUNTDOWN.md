# ⏰ Panduan Mengatur Countdown Timer

## 🎯 Cara Mudah Mengatur Tanggal dan Waktu Countdown

### 📝 Langkah 1: Buka File Konfigurasi
Buka file `assets/js/countdown-config.js` dengan text editor atau code editor.

### 📅 Langkah 2: Ubah Tanggal Acara
Cari baris ini dan ubah sesuai tanggal acara Anda:
```javascript
date: '2025-06-23',
```

**Format:** `YYYY-MM-DD` (Tahun-Bulan-Hari)

**Contoh:**
- `'2025-01-15'` = 15 Januari 2025
- `'2025-06-23'` = 23 Juni 2025
- `'2025-12-31'` = 31 Desember 2025

### ⏰ Langkah 3: Ubah Waktu Acara
Cari baris ini dan ubah sesuai waktu acara Anda:
```javascript
time: '08:00',
```

**Format:** `HH:MM` (24 jam format)

**Contoh:**
- `'08:00'` = Jam 8 pagi
- `'14:30'` = Jam 2:30 siang
- `'19:45'` = Jam 7:45 malam
- `'23:59'` = Jam 11:59 malam

### 🌍 Langkah 4: Pilih <PERSON>ona Waktu (Opsional)
```javascript
timezone: 'Asia/Jakarta',
```

**Pilihan untuk Indonesia:**
- `'Asia/Jakarta'` = WIB (Waktu Indonesia Barat)
- `'Asia/Makassar'` = WITA (Waktu Indonesia Tengah)
- `'Asia/Jayapura'` = WIT (Waktu Indonesia Timur)

### 💬 Langkah 5: Ubah Pesan (Opsional)
```javascript
completedMessage: 'Hari Bahagia Telah Tiba!',
runningMessage: 'Menuju Hari Bahagia',
```

### 💾 Langkah 6: Simpan File
Simpan file `countdown-config.js` setelah melakukan perubahan.

### 🔄 Langkah 7: Refresh Website
Buka atau refresh halaman website untuk melihat perubahan countdown.

---

## 📋 Contoh Konfigurasi Lengkap

### Contoh 1: Akad Nikah Pagi
```javascript
const COUNTDOWN_CONFIG = {
    date: '2025-07-15',           // 15 Juli 2025
    time: '08:00',                // Jam 8 pagi
    timezone: 'Asia/Jakarta',     // WIB
    completedMessage: 'Akad Nikah Dimulai!',
    runningMessage: 'Menuju Akad Nikah',
    enableCelebration: true,
    enableDebug: false,
    usePadding: true
};
```

### Contoh 2: Resepsi Sore
```javascript
const COUNTDOWN_CONFIG = {
    date: '2025-07-15',           // 15 Juli 2025
    time: '16:00',                // Jam 4 sore
    timezone: 'Asia/Jakarta',     // WIB
    completedMessage: 'Resepsi Dimulai!',
    runningMessage: 'Menuju Resepsi',
    enableCelebration: true,
    enableDebug: false,
    usePadding: true
};
```

### Contoh 3: Acara Malam
```javascript
const COUNTDOWN_CONFIG = {
    date: '2025-12-31',           // 31 Desember 2025
    time: '19:30',                // Jam 7:30 malam
    timezone: 'Asia/Jakarta',     // WIB
    completedMessage: 'Pesta Dimulai!',
    runningMessage: 'Menuju Pesta Tahun Baru',
    enableCelebration: true,
    enableDebug: false,
    usePadding: true
};
```

---

## ⚙️ Pengaturan Tambahan

### 🎊 Efek Perayaan
```javascript
enableCelebration: true,  // true = aktif, false = nonaktif
```
Mengaktifkan efek confetti dan animasi ketika countdown selesai.

### 🐛 Mode Debug
```javascript
enableDebug: true,        // true = aktif, false = nonaktif
```
Menampilkan informasi countdown di console browser (untuk debugging).

### 🔢 Format Angka
```javascript
usePadding: true,         // true = 01, 02, 03 | false = 1, 2, 3
```
Mengatur apakah angka ditampilkan dengan padding nol di depan.

---

## 🔍 Cara Mengecek Countdown

### 1. Buka Browser Console
- **Chrome/Edge:** Tekan `F12` atau `Ctrl+Shift+I`
- **Firefox:** Tekan `F12` atau `Ctrl+Shift+K`
- **Safari:** Tekan `Cmd+Option+I`

### 2. Lihat Tab Console
Anda akan melihat informasi seperti ini:
```
🎉 === COUNTDOWN TIMER INFO ===
📅 Target Date: Senin, 23 Juni 2025
⏰ Target Time: 08.00.00
🕐 Current Time: 23/6/2025 15.30.45
🌍 Timezone: Asia/Jakarta
💬 Running Message: Menuju Hari Bahagia
🎊 Completed Message: Hari Bahagia Telah Tiba!
✨ Celebration Effect: Enabled
🔧 Debug Mode: Disabled
📝 Number Padding: Enabled (01, 02, 03)
===============================
```

---

## ❗ Troubleshooting

### Countdown Tidak Berubah
1. Pastikan format tanggal benar: `YYYY-MM-DD`
2. Pastikan format waktu benar: `HH:MM`
3. Refresh halaman website
4. Cek console browser untuk error

### Tanggal Tidak Valid
Jika muncul error, pastikan:
- Bulan antara 01-12
- Hari sesuai dengan bulan (01-31)
- Tahun dalam format 4 digit (2025, 2026, dst)

### Waktu Tidak Valid
Pastikan:
- Jam antara 00-23
- Menit antara 00-59
- Gunakan format 24 jam

---

## 📞 Tips & Trik

### 💡 Tip 1: Test dengan Waktu Dekat
Untuk testing, set countdown ke beberapa menit ke depan:
```javascript
date: '2024-06-23',  // Hari ini
time: '15:35',       // 5 menit dari sekarang
```

### 💡 Tip 2: Backup Konfigurasi
Simpan copy konfigurasi asli sebelum mengubah:
```javascript
// BACKUP - Konfigurasi asli
// date: '2025-06-23',
// time: '08:00',

// KONFIGURASI BARU
date: '2025-07-15',
time: '16:00',
```

### 💡 Tip 3: Multiple Events
Untuk acara dengan beberapa sesi, buat konfigurasi terpisah dan ganti sesuai kebutuhan.

---

**🎉 Selamat! Countdown timer Anda siap digunakan!**
