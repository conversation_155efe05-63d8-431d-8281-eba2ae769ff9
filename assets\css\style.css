/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #2c3e50;
    overflow-x: hidden;
    background: #faf8f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Color Variables */
:root {
    --primary-color: #d4af37;
    --secondary-color: #8b4513;
    --accent-color: #f4e4c1;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --white: #ffffff;
    --cream: #faf8f5;
    --gold-light: #f7e7ce;
    --rose-gold: #e8b4b8;
    --deep-burgundy: #722f37;
    --blue-primary: #4a90e2;
    --blue-secondary: #357abd;
    --blue-light: #e3f2fd;
    --navy-blue: #1e3a8a;
    --sky-blue: #87ceeb;
}

/* Welcome Screen */
.welcome-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--blue-primary) 0%, var(--navy-blue) 50%, var(--deep-burgundy) 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    transition: all 0.8s ease;
    overflow: hidden;
}

.welcome-screen.hidden {
    opacity: 0;
    transform: scale(1.1);
    pointer-events: none;
}

.welcome-bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="welcome-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="5" cy="15" r="0.5" fill="%23d4af37" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23welcome-pattern)"/></svg>');
    opacity: 0.3;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.welcome-content {
    text-align: center;
    color: var(--white);
    position: relative;
    z-index: 2;
    max-width: 500px;
    padding: 3rem 2rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 30px;
    border: 2px solid rgba(212, 175, 55, 0.3);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.welcome-ornament {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    animation: heartbeat 2s ease-in-out infinite;
}

.welcome-title {
    font-family: 'Great Vibes', 'Dancing Script', cursive;
    font-size: 4.5rem;
    font-weight: 400;
    margin-bottom: 0.8rem;
    color: var(--gold-light);
    text-shadow: 3px 3px 8px rgba(0, 0, 0, 0.5);
    letter-spacing: 2px;
    line-height: 1.1;
    text-align: center;
}

.welcome-subtitle {
    font-family: 'Playfair Display', 'Poppins', serif;
    font-size: 1.5rem;
    margin-bottom: 0.8rem;
    color: var(--blue-light);
    font-weight: 500;
    letter-spacing: 4px;
    text-transform: uppercase;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
    font-style: italic;
}

.welcome-date {
    font-family: 'Poppins', sans-serif;
    font-size: 1.2rem;
    font-weight: 500;
    margin-bottom: 2.5rem;
    color: var(--white);
    opacity: 0.95;
    letter-spacing: 1px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-open-invitation {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--blue-primary) 100%);
    color: var(--white);
    border: none;
    padding: 1.3rem 3.5rem;
    border-radius: 60px;
    font-family: 'Playfair Display', 'Poppins', serif;
    font-size: 1.4rem;
    font-weight: 600;
    letter-spacing: 2px;
    text-transform: capitalize;
    cursor: pointer;
    transition: all 0.4s ease;
    box-shadow: 0 12px 35px rgba(212, 175, 55, 0.5);
    border: 3px solid rgba(255, 255, 255, 0.4);
    animation: pulse-glow 2s infinite;
    position: relative;
    overflow: hidden;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-open-invitation::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-open-invitation:hover::before {
    left: 100%;
}

.btn-open-invitation:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 15px 40px rgba(212, 175, 55, 0.6);
    background: linear-gradient(135deg, var(--blue-primary) 0%, var(--primary-color) 100%);
    border-color: rgba(255, 255, 255, 0.5);
}

.btn-open-invitation:active {
    transform: translateY(-2px) scale(1.02);
}

.btn-open-invitation i {
    margin-right: 0.8rem;
    font-size: 1.2rem;
}

@keyframes pulse-glow {
    0%, 100% { box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4); }
    50% { box-shadow: 0 8px 35px rgba(74, 144, 226, 0.6); }
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--deep-burgundy) 0%, var(--secondary-color) 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
    opacity: 0;
    pointer-events: none;
}

.loading-screen.active {
    opacity: 1;
    pointer-events: all;
}

.loading-content {
    text-align: center;
    color: var(--white);
}

.heart-animation {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
    animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.loading-content h2 {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    color: var(--gold-light);
}

/* Music Control */
.music-control {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.music-btn {
    width: 55px;
    height: 55px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--blue-primary) 0%, var(--primary-color) 100%);
    border: 3px solid var(--white);
    color: var(--white);
    font-size: 1.2rem;
    cursor: pointer;
    box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
}

.music-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
}

.music-btn.paused {
    animation: none;
    opacity: 0.7;
    background: linear-gradient(135deg, var(--text-light) 0%, var(--text-dark) 100%);
}

@keyframes pulse {
    0%, 100% { box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4); }
    50% { box-shadow: 0 8px 30px rgba(74, 144, 226, 0.6); }
}

/* Hero Section */
.hero-section {
    height: 100vh;
    background: linear-gradient(135deg, var(--blue-primary) 0%, var(--navy-blue) 30%, var(--deep-burgundy) 70%, var(--primary-color) 100%),
                url('https://images.unsplash.com/photo-1519904981063-b0cf448d479e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80') center/cover;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hero-pattern" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="2" fill="%23ffffff" opacity="0.1"/><circle cx="10" cy="30" r="1" fill="%23d4af37" opacity="0.15"/><circle cx="30" cy="10" r="1.5" fill="%234a90e2" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23hero-pattern)"/></svg>');
    opacity: 0.4;
    animation: float 8s ease-in-out infinite;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(74, 144, 226, 0.2) 0%, rgba(212, 175, 55, 0.1) 50%, rgba(114, 47, 55, 0.3) 100%);
}

.hero-content {
    position: relative;
    z-index: 3;
    max-width: 950px;
    padding: 0 20px;
    background: rgba(255, 255, 255, 0.98);
    border-radius: 35px;
    padding: 4.5rem 3.5rem;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(15px);
    border: 3px solid transparent;
    background-clip: padding-box;
    position: relative;
}

.hero-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 35px;
    padding: 3px;
    background: linear-gradient(135deg, var(--primary-color), var(--blue-primary), var(--deep-burgundy));
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    z-index: -1;
}

.wedding-date {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    color: var(--secondary-color);
    font-weight: 500;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.couple-names {
    font-family: 'Dancing Script', cursive;
    font-size: 4.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--deep-burgundy);
    text-shadow: 2px 2px 4px rgba(212, 175, 55, 0.2);
    line-height: 1.2;
}

.groom-name, .bride-name {
    display: inline-block;
    animation: fadeInUp 1s ease-out;
    color: var(--primary-color);
}

.ampersand {
    font-size: 3.5rem;
    margin: 0 1.5rem;
    color: var(--deep-burgundy);
    font-weight: 400;
}

.wedding-tagline {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #000000;
    font-style: italic;
    font-weight: 300;
    line-height: 1.6;
}

.wedding-tagline p {
    color: #000000;
    margin-bottom: 1rem;
    text-shadow: none;
}

/* Styling khusus untuk teks Arab */
.wedding-tagline p:first-child {
    font-size: 1.1rem;
    font-weight: 500;
    text-align: center;
    margin-bottom: 1.5rem;
}

.wedding-tagline p:nth-child(2) {
    font-size: 1.1rem;
    font-weight: 400;
    text-align: center;
    margin-bottom: 1.5rem;
    line-height: 1.8;
}

.hero-ornaments {
    margin-top: 2rem;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 3rem;
}

.ornament {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--blue-primary), var(--primary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.5rem;
    box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
    animation: rotate 4s linear infinite;
}

.ornament-left {
    animation-direction: normal;
}

.ornament-right {
    animation-direction: reverse;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1.8rem;
    color: var(--blue-primary);
    animation: bounce 2s infinite;
    background: rgba(255, 255, 255, 0.9);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(250, 248, 245, 0.98);
    backdrop-filter: blur(15px);
    z-index: 1000;
    transition: all 0.3s ease;
    transform: translateY(-100%);
    border-bottom: 1px solid var(--primary-color);
}

.navbar.scrolled {
    transform: translateY(0);
    box-shadow: 0 4px 30px rgba(212, 175, 55, 0.15);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.2rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    font-family: 'Dancing Script', cursive;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    text-shadow: 1px 1px 2px rgba(212, 175, 55, 0.3);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
    font-size: 1rem;
}

.nav-link:hover {
    color: var(--primary-color);
    transform: translateY(-2px);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
}

.section-header::before {
    content: '';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--blue-primary), var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

.section-title {
    font-family: 'Dancing Script', cursive;
    font-size: 3.5rem;
    color: var(--deep-burgundy);
    margin-bottom: 1rem;
    position: relative;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
    font-weight: 300;
}

/* Couple Section */
.couple-section {
    padding: 6rem 0;
    background: linear-gradient(135deg, var(--cream) 0%, var(--gold-light) 100%);
    position: relative;
}

.couple-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23d4af37" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23d4af37" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.couple-container {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1100px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.couple-card {
    background: var(--white);
    border-radius: 25px;
    padding: 3rem 2rem;
    text-align: center;
    box-shadow: 0 15px 50px rgba(212, 175, 55, 0.15);
    transition: all 0.3s ease;
    border: 2px solid var(--accent-color);
    position: relative;
    overflow: hidden;
}

.couple-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(212, 175, 55, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

.couple-card:hover::before {
    opacity: 1;
    transform: rotate(45deg) translate(50%, 50%);
}

.couple-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 25px 70px rgba(212, 175, 55, 0.25);
}

.couple-image {
    width: 220px;
    height: 220px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 2rem;
    border: 6px solid var(--primary-color);
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.3);
    position: relative;
}

.couple-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    box-shadow: inset 0 0 20px rgba(212, 175, 55, 0.2);
}

.couple-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.couple-card:hover .couple-image img {
    transform: scale(1.1);
}

.couple-info h3 {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    color: var(--deep-burgundy);
    margin-bottom: 1rem;
    position: relative;
}

.couple-info p {
    margin-bottom: 0.5rem;
    color: var(--text-light);
    font-weight: 400;
}

.couple-info strong {
    color: var(--text-dark);
    font-weight: 600;
}

.social-links {
    margin-top: 1.5rem;
}

.social-link {
    display: inline-block;
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    border-radius: 50%;
    text-decoration: none;
    line-height: 45px;
    margin: 0 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.social-link:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--deep-burgundy));
    transform: scale(1.15) rotate(5deg);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

.couple-divider {
    display: flex;
    justify-content: center;
    align-items: center;
}

.heart-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--blue-primary) 0%, var(--primary-color) 50%, var(--secondary-color) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 2rem;
    animation: heartbeat 2s ease-in-out infinite;
    box-shadow: 0 10px 30px rgba(74, 144, 226, 0.4);
    border: 4px solid var(--white);
}

/* Countdown Section */
.countdown-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, var(--deep-burgundy) 0%, var(--secondary-color) 100%);
    color: var(--white);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.countdown-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hearts" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M25,35 C25,25 15,25 15,35 C15,25 5,25 5,35 C5,45 25,55 25,55 C25,55 45,45 45,35 C45,25 35,25 35,35 C35,25 25,25 25,35 Z" fill="%23d4af37" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23hearts)"/></svg>');
    opacity: 0.2;
}

.countdown-section h2 {
    font-family: 'Dancing Script', cursive;
    font-size: 3rem;
    margin-bottom: 3rem;
    color: var(--gold-light);
    position: relative;
    z-index: 2;
}

.countdown-timer {
    display: flex;
    justify-content: center;
    gap: 2.5rem;
    flex-wrap: wrap;
    position: relative;
    z-index: 2;
}

.time-unit {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    padding: 2rem 1.5rem;
    min-width: 120px;
    backdrop-filter: blur(15px);
    border: 2px solid rgba(212, 175, 55, 0.3);
    transition: all 0.3s ease;
}

.time-unit:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--primary-color);
}

.time-number {
    display: block;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.time-label {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
}

/* Event Section */
.event-section {
    padding: 6rem 0;
    background: var(--white);
    position: relative;
}

.event-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
    max-width: 900px;
    margin: 0 auto;
}

.event-card {
    background: linear-gradient(135deg, var(--cream) 0%, var(--white) 100%);
    border-radius: 25px;
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid var(--accent-color);
    position: relative;
    overflow: hidden;
}

.event-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.event-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(212, 175, 55, 0.2);
    border-color: var(--primary-color);
}

.event-icon {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    color: var(--white);
    font-size: 2.5rem;
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.3);
    border: 4px solid var(--white);
}

.event-card h3 {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    color: var(--deep-burgundy);
    margin-bottom: 1.5rem;
}

.event-details {
    margin-bottom: 2rem;
}

.event-details p {
    margin-bottom: 1rem;
    color: var(--text-dark);
    font-weight: 500;
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Styling khusus untuk alamat yang panjang */
.event-details p:last-child {
    line-height: 1.7;
    text-align: center;
}

.event-details i {
    color: var(--primary-color);
    margin-right: 0.8rem;
    width: 25px;
    font-size: 1.1rem;
}

.btn-location {
    display: inline-block;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--white);
    text-decoration: none;
    padding: 1rem 2rem;
    border-radius: 30px;
    margin-top: 1rem;
    transition: all 0.3s ease;
    font-weight: 600;
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.3);
    border: 2px solid transparent;
}

.btn-location:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.4);
    border-color: var(--white);
}

/* Gallery Section */
.gallery-section {
    padding: 6rem 0;
    background: linear-gradient(135deg, var(--gold-light) 0%, var(--cream) 100%);
    position: relative;
}

.gallery-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="flowers" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="2" fill="%23d4af37" opacity="0.1"/><circle cx="10" cy="30" r="1.5" fill="%238b4513" opacity="0.1"/><circle cx="30" cy="10" r="1" fill="%23d4af37" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23flowers)"/></svg>');
    opacity: 0.3;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
    position: relative;
    z-index: 2;
}

.gallery-item {
    border-radius: 20px;
    overflow: hidden;
    aspect-ratio: 1;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.2);
    border: 3px solid var(--white);
    position: relative;
}

.gallery-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(212, 175, 55, 0.2), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 2;
}

.gallery-item:hover::before {
    opacity: 1;
}

.gallery-item:hover {
    transform: scale(1.05) rotate(2deg);
    box-shadow: 0 20px 50px rgba(212, 175, 55, 0.3);
    border-color: var(--primary-color);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

/* Wedding Gift Section */
.wedding-gift-section {
    padding: 6rem 0;
    background: linear-gradient(135deg, var(--blue-light) 0%, var(--cream) 100%);
    position: relative;
}

.wedding-gift-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="gift-pattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="%23d4af37" opacity="0.1"/><circle cx="5" cy="25" r="0.5" fill="%234a90e2" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23gift-pattern)"/></svg>');
    opacity: 0.3;
}

.gift-container {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.gift-card {
    background: var(--white);
    border-radius: 20px;
    margin-bottom: 1.5rem;
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.15);
    border: 2px solid var(--accent-color);
    overflow: hidden;
    transition: all 0.3s ease;
}

.gift-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(212, 175, 55, 0.2);
}

.gift-header {
    display: flex;
    align-items: center;
    padding: 1.5rem 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, var(--white) 0%, var(--cream) 100%);
}

.gift-header:hover {
    background: linear-gradient(135deg, var(--cream) 0%, var(--gold-light) 100%);
}

.gift-header:hover .bank-info p {
    color: var(--text-dark);
    font-weight: 600;
}

.bank-logo {
    width: 60px;
    height: 60px;
    margin-right: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: 0.5rem;
}

.bank-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.bank-info {
    flex: 1;
}

.bank-info h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.bank-info p {
    color: var(--text-dark);
    font-size: 1rem;
    font-weight: 500;
    margin: 0;
    opacity: 1;
    line-height: 1.4;
    text-shadow: none;
    display: block;
    visibility: visible;
}

.dropdown-toggle {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--blue-primary));
    border-radius: 50%;
    color: var(--white);
    transition: all 0.3s ease;
}

.dropdown-toggle i {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.gift-card.active .dropdown-toggle {
    background: linear-gradient(135deg, var(--blue-primary), var(--primary-color));
}

.gift-card.active .dropdown-toggle i {
    transform: rotate(180deg);
}

.gift-content {
    padding: 0 2rem 2rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.4s ease;
    opacity: 0;
}

.gift-card.active .gift-content {
    max-height: 200px;
    opacity: 1;
    padding: 1.5rem 2rem 2rem;
}

.account-number {
    margin-bottom: 1.5rem;
}

.account-number label,
.account-name label {
    display: block;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.number-display {
    display: flex;
    align-items: center;
    background: var(--cream);
    border-radius: 10px;
    padding: 1rem;
    border: 2px solid var(--accent-color);
}

.account-digits {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-dark);
    letter-spacing: 2px;
}

.copy-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--blue-primary));
    color: var(--white);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.copy-btn:hover {
    background: linear-gradient(135deg, var(--blue-primary), var(--primary-color));
    transform: scale(1.1);
}

.copy-btn:active {
    transform: scale(0.95);
}

.account-name span {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--text-dark);
}

.gift-note {
    text-align: center;
    margin-top: 3rem;
    position: relative;
    z-index: 2;
}

.gift-note p {
    font-size: 1.1rem;
    color: var(--text-dark);
    font-style: italic;
    background: rgba(255, 255, 255, 0.9);
    padding: 1rem 2rem;
    border-radius: 25px;
    display: inline-block;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.2);
}

.gift-note i {
    color: var(--rose-gold);
    margin-right: 0.5rem;
}

/* RSVP Section */
.rsvp-section {
    padding: 6rem 0;
    background: var(--white);
    position: relative;
}

.rsvp-form-container {
    max-width: 700px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.rsvp-form {
    background: linear-gradient(135deg, var(--cream) 0%, var(--white) 100%);
    padding: 3rem;
    border-radius: 25px;
    box-shadow: 0 15px 50px rgba(212, 175, 55, 0.15);
    border: 2px solid var(--accent-color);
    position: relative;
    overflow: hidden;
}

.rsvp-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.form-group {
    margin-bottom: 2rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.8rem;
    font-weight: 600;
    color: var(--text-dark);
    font-size: 1.1rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem 1.2rem;
    border: 2px solid var(--accent-color);
    border-radius: 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--white);
    font-family: 'Poppins', sans-serif;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
    transform: translateY(-2px);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.btn-submit {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--white);
    border: none;
    padding: 1.2rem;
    border-radius: 15px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.3);
    border: 2px solid transparent;
}

.btn-submit:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.4);
    border-color: var(--white);
}

.btn-submit:active {
    transform: translateY(-1px);
}

/* Footer */
.footer {
    background: linear-gradient(135deg, var(--deep-burgundy) 0%, var(--text-dark) 100%);
    color: var(--white);
    padding: 4rem 0 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stars" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23d4af37" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23stars)"/></svg>');
    opacity: 0.2;
}

.footer-content {
    position: relative;
    z-index: 2;
}

.footer-content h3 {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.footer-content p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.footer-social {
    margin: 2rem 0;
}

.footer-social .social-link {
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 1.2rem;
    margin: 0 0.8rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: 2px solid var(--white);
}

.footer-social .social-link:hover {
    background: linear-gradient(135deg, var(--white), var(--accent-color));
    color: var(--deep-burgundy);
    transform: scale(1.2) rotate(-5deg);
}

.footer-bottom {
    border-top: 1px solid rgba(212, 175, 55, 0.3);
    padding-top: 1.5rem;
    margin-top: 2rem;
    opacity: 0.8;
    position: relative;
    z-index: 2;
}

.footer-bottom i {
    color: var(--rose-gold);
    animation: heartbeat 1.5s ease-in-out infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .welcome-content {
        padding: 2.5rem 2rem;
        margin: 0 1rem;
        max-width: 400px;
    }

    .welcome-title {
        font-size: 3.2rem;
        letter-spacing: 1px;
        margin-bottom: 0.6rem;
    }

    .welcome-subtitle {
        font-size: 1.2rem;
        letter-spacing: 2px;
        margin-bottom: 0.6rem;
    }

    .welcome-date {
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    .btn-open-invitation {
        padding: 1.1rem 2.5rem;
        font-size: 1.2rem;
        letter-spacing: 1px;
    }

    .btn-open-invitation i {
        margin-right: 0.6rem;
        font-size: 1.1rem;
    }

    .hero-content {
        padding: 2.5rem 2rem;
        margin: 0 1rem;
    }

    .couple-names {
        font-size: 3rem;
    }

    .ampersand {
        font-size: 2.5rem;
        margin: 0 0.8rem;
    }

    .wedding-tagline {
        font-size: 1rem;
    }

    .wedding-tagline p:first-child {
        font-size: 1rem;
    }

    .wedding-tagline p:nth-child(2) {
        font-size: 1rem;
        line-height: 1.7;
    }

    .nav-container {
        padding: 1rem 1.5rem;
    }

    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: rgba(250, 248, 245, 0.98);
        backdrop-filter: blur(15px);
        flex-direction: column;
        justify-content: start;
        align-items: center;
        padding-top: 3rem;
        transition: left 0.3s ease;
        gap: 1.5rem;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }

    .couple-container {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .couple-divider {
        order: 2;
    }

    .couple-card {
        padding: 2rem 1.5rem;
    }

    .couple-image {
        width: 180px;
        height: 180px;
    }

    .countdown-timer {
        gap: 1.5rem;
    }

    .time-unit {
        min-width: 90px;
        padding: 1.5rem 1rem;
    }

    .time-number {
        font-size: 2.2rem;
    }

    .section-title {
        font-size: 2.8rem;
    }

    .event-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }

    .gift-header {
        padding: 1.2rem 1.5rem;
    }

    .bank-logo {
        width: 50px;
        height: 50px;
        margin-right: 1rem;
    }

    .bank-info h3 {
        font-size: 1.1rem;
    }

    .bank-info p {
        font-size: 0.9rem;
        color: var(--text-dark);
        font-weight: 500;
    }

    .account-digits {
        font-size: 1.1rem;
        letter-spacing: 1px;
    }

    .gift-note p {
        font-size: 1rem;
        padding: 0.8rem 1.5rem;
    }

    .rsvp-form {
        padding: 2rem 1.5rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .welcome-content {
        padding: 2rem 1.5rem;
        margin: 0 0.5rem;
        max-width: 350px;
    }

    .welcome-title {
        font-size: 2.8rem;
        letter-spacing: 0px;
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .welcome-subtitle {
        font-size: 1rem;
        letter-spacing: 1px;
        margin-bottom: 0.5rem;
    }

    .welcome-date {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }

    .btn-open-invitation {
        padding: 1rem 2rem;
        font-size: 1.1rem;
        letter-spacing: 0px;
        border-radius: 50px;
    }

    .btn-open-invitation i {
        margin-right: 0.5rem;
        font-size: 1rem;
    }

    .hero-content {
        padding: 2rem 1.5rem;
        margin: 0 0.5rem;
    }

    .couple-names {
        font-size: 2.5rem;
        line-height: 1.1;
    }

    .ampersand {
        font-size: 2rem;
        margin: 0 0.5rem;
    }

    .wedding-date {
        font-size: 1.1rem;
    }

    .wedding-tagline {
        font-size: 0.95rem;
    }

    .wedding-tagline p:first-child {
        font-size: 0.95rem;
    }

    .wedding-tagline p:nth-child(2) {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .section-title {
        font-size: 2.2rem;
    }

    .section-subtitle {
        font-size: 1rem;
    }

    .couple-card {
        padding: 1.5rem 1rem;
    }

    .couple-image {
        width: 150px;
        height: 150px;
        border-width: 4px;
    }

    .couple-info h3 {
        font-size: 2rem;
    }

    .heart-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .countdown-timer {
        gap: 1rem;
    }

    .time-unit {
        min-width: 75px;
        padding: 1rem 0.8rem;
    }

    .time-number {
        font-size: 1.8rem;
    }

    .time-label {
        font-size: 0.85rem;
    }

    .event-card {
        padding: 2rem 1.5rem;
    }

    .event-details p:last-child {
        font-size: 1rem;
        line-height: 1.6;
    }

    .event-icon {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 1rem;
    }

    .rsvp-form {
        padding: 1.5rem 1rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.8rem 1rem;
    }

    .gift-header {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .bank-logo {
        width: 45px;
        height: 45px;
        margin-right: 0;
        margin-bottom: 0.5rem;
    }

    .bank-info h3 {
        font-size: 1rem;
    }

    .bank-info p {
        font-size: 0.85rem;
        color: var(--text-dark);
        font-weight: 500;
        margin-top: 0.3rem;
    }

    .number-display {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .account-digits {
        font-size: 1rem;
        letter-spacing: 0px;
    }

    .gift-note p {
        font-size: 0.9rem;
        padding: 0.7rem 1rem;
    }

    .btn-submit {
        padding: 1rem;
        font-size: 1.1rem;
    }

    .music-control {
        top: 15px;
        right: 15px;
    }

    .music-btn {
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }

    .footer-content h3 {
        font-size: 2rem;
    }

    .footer-social .social-link {
        width: 45px;
        height: 45px;
        line-height: 45px;
        margin: 0 0.5rem;
    }
}

/* Extra small devices */
@media (max-width: 320px) {
    .welcome-content {
        padding: 1.5rem 1rem;
        margin: 0 0.25rem;
        max-width: 300px;
    }

    .welcome-title {
        font-size: 2.2rem;
        margin-bottom: 0.4rem;
    }

    .welcome-subtitle {
        font-size: 0.9rem;
        letter-spacing: 0px;
        margin-bottom: 0.4rem;
    }

    .welcome-date {
        font-size: 0.8rem;
        margin-bottom: 1.2rem;
    }

    .btn-open-invitation {
        padding: 0.9rem 1.8rem;
        font-size: 1rem;
        border-radius: 45px;
    }

    .btn-open-invitation i {
        margin-right: 0.4rem;
        font-size: 0.9rem;
    }

    .hero-content {
        padding: 1.5rem 1rem;
    }

    .couple-names {
        font-size: 2rem;
    }

    .ampersand {
        font-size: 1.5rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .time-unit {
        min-width: 65px;
        padding: 0.8rem 0.5rem;
    }

    .time-number {
        font-size: 1.5rem;
    }

    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
