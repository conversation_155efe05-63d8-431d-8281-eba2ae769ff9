/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading-content {
    text-align: center;
    color: white;
}

.heart-animation {
    font-size: 3rem;
    margin-bottom: 1rem;
    animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.loading-content h2 {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

/* Music Control */
.music-control {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.music-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
}

.music-btn:hover {
    transform: scale(1.1);
}

.music-btn.paused {
    animation: none;
    opacity: 0.7;
}

@keyframes pulse {
    0%, 100% { box-shadow: 0 4px 15px rgba(0,0,0,0.2); }
    50% { box-shadow: 0 4px 25px rgba(102, 126, 234, 0.4); }
}

/* Hero Section */
.hero-section {
    height: 100vh;
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), 
                url('https://images.unsplash.com/photo-1519741497674-611481863552?ixlib=rb-4.0.3') center/cover;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    position: relative;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.wedding-date {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.couple-names {
    font-family: 'Dancing Script', cursive;
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.groom-name, .bride-name {
    display: inline-block;
    animation: fadeInUp 1s ease-out;
}

.ampersand {
    font-size: 3rem;
    margin: 0 1rem;
    opacity: 0.8;
}

.wedding-tagline {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    font-style: italic;
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
    transform: translateY(-100%);
}

.navbar.scrolled {
    transform: translateY(0);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    font-family: 'Dancing Script', cursive;
    font-size: 1.8rem;
    font-weight: 700;
    color: #667eea;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #667eea;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #667eea;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-family: 'Dancing Script', cursive;
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Couple Section */
.couple-section {
    padding: 5rem 0;
    background: #f8f9fa;
}

.couple-container {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 3rem;
    align-items: center;
    max-width: 1000px;
    margin: 0 auto;
}

.couple-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.couple-card:hover {
    transform: translateY(-10px);
}

.couple-image {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 1.5rem;
    border: 5px solid #667eea;
}

.couple-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.couple-info h3 {
    font-family: 'Dancing Script', cursive;
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.couple-info p {
    margin-bottom: 0.5rem;
    color: #666;
}

.social-links {
    margin-top: 1rem;
}

.social-link {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    text-decoration: none;
    line-height: 40px;
    margin: 0 0.5rem;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #764ba2;
    transform: scale(1.1);
}

.couple-divider {
    display: flex;
    justify-content: center;
    align-items: center;
}

.heart-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    animation: heartbeat 2s ease-in-out infinite;
}

/* Countdown Section */
.countdown-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.countdown-section h2 {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    margin-bottom: 2rem;
}

.countdown-timer {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.time-unit {
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 1.5rem;
    min-width: 100px;
    backdrop-filter: blur(10px);
}

.time-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.time-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Event Section */
.event-section {
    padding: 5rem 0;
    background: white;
}

.event-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.event-card {
    background: #f8f9fa;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: transform 0.3s ease;
}

.event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.event-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.event-card h3 {
    font-family: 'Dancing Script', cursive;
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.event-details p {
    margin-bottom: 0.5rem;
    color: #666;
}

.event-details i {
    color: #667eea;
    margin-right: 0.5rem;
    width: 20px;
}

.btn-location {
    display: inline-block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.btn-location:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Gallery Section */
.gallery-section {
    padding: 5rem 0;
    background: #f8f9fa;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.gallery-item {
    border-radius: 15px;
    overflow: hidden;
    aspect-ratio: 1;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

/* RSVP Section */
.rsvp-section {
    padding: 5rem 0;
    background: white;
}

.rsvp-form-container {
    max-width: 600px;
    margin: 0 auto;
}

.rsvp-form {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 20px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.btn-submit {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 10px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 3rem 0 1rem;
    text-align: center;
}

.footer-content h3 {
    font-family: 'Dancing Script', cursive;
    font-size: 2rem;
    margin-bottom: 1rem;
}

.footer-social {
    margin: 1.5rem 0;
}

.footer-bottom {
    border-top: 1px solid #555;
    padding-top: 1rem;
    margin-top: 2rem;
    opacity: 0.8;
}

.footer-bottom i {
    color: #e74c3c;
}

/* Responsive Design */
@media (max-width: 768px) {
    .couple-names {
        font-size: 2.5rem;
    }
    
    .ampersand {
        font-size: 2rem;
        margin: 0 0.5rem;
    }
    
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: white;
        flex-direction: column;
        justify-content: start;
        align-items: center;
        padding-top: 2rem;
        transition: left 0.3s ease;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .hamburger {
        display: flex;
    }
    
    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }
    
    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }
    
    .couple-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .couple-divider {
        order: 2;
    }
    
    .countdown-timer {
        gap: 1rem;
    }
    
    .time-unit {
        min-width: 80px;
        padding: 1rem;
    }
    
    .time-number {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 2.5rem;
    }
    
    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .couple-names {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .couple-image {
        width: 150px;
        height: 150px;
    }
    
    .music-control {
        top: 15px;
        right: 15px;
    }
    
    .music-btn {
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }
}
