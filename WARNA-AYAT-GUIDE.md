# 🎨 Panduan Warna Ayat Al-Quran

## ✨ Perubahan yang Telah Dibuat

### 📝 **Warna Teks Ayat Al-Quran**
Bagian `wedding-tagline` yang berisi ayat Al-Quran telah diubah menjadi **warna hitam (#000000)** untuk:
- Meningkatkan kontras dan readability
- Memberikan kesan formal dan sakral
- Memastikan teks mudah dibaca di background putih

### 🎯 **Implementasi CSS**

```css
.wedding-tagline {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #000000;  /* Warna hitam */
    font-style: italic;
    font-weight: 300;
    line-height: 1.6;
}

.wedding-tagline p {
    color: #000000;  /* Semua paragraf hitam */
    margin-bottom: 1rem;
    text-shadow: none;  /* Hilangkan shadow */
}
```

### 📖 **Struktur HTML yang Diperbaiki**

```html
<div class="wedding-tagline">
    <p>بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ</p>
    
    <p>"وَمِنْ آيَاتِهِ أَنْ خَلَقَ لَكُم مِّنْ أَنفُسِكُمْ أَزْوَاجًا لِّتَسْكُنُوا إِلَيْهَا وَجَعَلَ بَيْنَكُم مَّوَدَّةً وَرَحْمَةً ۚ إِنَّ فِي ذَٰلِكَ لَآيَاتٍ لِّقَوْمٍ يَتَفَكَّرُونَ"</p>
    
    <p>"Dan di antara tanda-tanda (kebesaran)-Nya ialah Dia menciptakan pasangan-pasangan untukmu dari jenismu sendiri, agar kamu cenderung dan merasa tenteram kepadanya. Dan Dia menjadikan di antaramu rasa kasih dan sayang..."</p>
    
    <p><strong>— QS. Ar-Rum: 21</strong></p>
</div>
```

## 🎨 **Styling Khusus untuk Teks Arab**

### **Basmalah (بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ)**
```css
.wedding-tagline p:first-child {
    font-size: 1.1rem;
    font-weight: 500;
    text-align: center;
    margin-bottom: 1.5rem;
}
```

### **Ayat Al-Quran (Arab)**
```css
.wedding-tagline p:nth-child(2) {
    font-size: 1.1rem;
    font-weight: 400;
    text-align: center;
    margin-bottom: 1.5rem;
    line-height: 1.8;
}
```

## 📱 **Responsive Design**

### **Mobile (≤ 768px)**
```css
.wedding-tagline {
    font-size: 1rem;
}

.wedding-tagline p:first-child {
    font-size: 1rem;
}

.wedding-tagline p:nth-child(2) {
    font-size: 1rem;
    line-height: 1.7;
}
```

### **Small Mobile (≤ 480px)**
```css
.wedding-tagline {
    font-size: 0.95rem;
}

.wedding-tagline p:first-child {
    font-size: 0.95rem;
}

.wedding-tagline p:nth-child(2) {
    font-size: 0.95rem;
    line-height: 1.6;
}
```

## 🎯 **Keunggulan Warna Hitam**

### ✅ **Kontras Optimal**
- Warna hitam memberikan kontras maksimal dengan background putih
- Mudah dibaca dalam segala kondisi pencahayaan
- Tidak mengganggu mata saat membaca

### ✅ **Kesan Formal dan Sakral**
- Warna hitam memberikan kesan formal untuk teks religius
- Menunjukkan penghormatan terhadap ayat suci
- Sesuai dengan tradisi penulisan teks keagamaan

### ✅ **Accessibility**
- Memenuhi standar WCAG untuk kontras warna
- Mudah dibaca oleh semua kalangan
- Tidak bergantung pada kemampuan melihat warna

## 🔧 **Cara Mengubah Warna**

### **Mengubah ke Warna Lain**
Edit di `assets/css/style.css`:

```css
/* Warna biru gelap */
.wedding-tagline {
    color: #1e3a8a;
}

/* Warna coklat gelap */
.wedding-tagline {
    color: #8b4513;
}

/* Warna abu-abu gelap */
.wedding-tagline {
    color: #374151;
}
```

### **Menambahkan Gradient Text**
```css
.wedding-tagline {
    background: linear-gradient(135deg, #000000, #374151);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
```

### **Menambahkan Text Shadow**
```css
.wedding-tagline {
    color: #000000;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}
```

## 💡 **Tips Styling Ayat Al-Quran**

### **1. Font yang Sesuai**
- Gunakan font yang mendukung karakter Arab
- Pastikan font memiliki Unicode support yang baik
- Pertimbangkan font khusus Arab seperti Amiri atau Scheherazade

### **2. Line Height yang Tepat**
- Teks Arab membutuhkan line-height yang lebih besar
- Gunakan minimal 1.6 untuk readability
- Sesuaikan dengan panjang ayat

### **3. Text Direction**
- Teks Arab dibaca dari kanan ke kiri (RTL)
- Gunakan `direction: rtl` jika diperlukan
- Pastikan alignment yang tepat

### **4. Responsive Considerations**
- Font size yang lebih kecil untuk mobile
- Margin dan padding yang proporsional
- Line height yang tetap optimal

## 🎨 **Alternatif Color Scheme**

### **Classic Black & White**
```css
.wedding-tagline { color: #000000; }
/* Background: white */
```

### **Elegant Dark Gray**
```css
.wedding-tagline { color: #2d3748; }
/* Background: cream/light gray */
```

### **Navy Blue**
```css
.wedding-tagline { color: #1e3a8a; }
/* Background: light blue/white */
```

### **Deep Brown**
```css
.wedding-tagline { color: #8b4513; }
/* Background: cream/beige */
```

## 📋 **Checklist Styling Ayat**

- [x] Warna hitam untuk kontras optimal
- [x] Font size yang readable
- [x] Line height yang sesuai untuk teks Arab
- [x] Margin dan spacing yang proporsional
- [x] Responsive design untuk semua device
- [x] Text shadow dihilangkan untuk clarity
- [x] Struktur HTML yang semantic
- [x] Styling khusus untuk basmalah
- [x] Bold untuk referensi ayat

---

**🎉 Ayat Al-Quran sekarang tampil dengan warna hitam yang elegant dan mudah dibaca!**
