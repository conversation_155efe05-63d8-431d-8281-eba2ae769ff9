# 💳 Panduan Wedding Gift Section

## ✨ Fitur Wedding Gift yang Telah Dibuat

### 🎯 **Fitur Utama**
- **Dropdown Animation**: Smooth expand/collapse untuk setiap bank
- **Copy Button**: One-click copy nomor rekening ke clipboard
- **Bank Logos**: Logo resmi bank dari Wikipedia
- **Responsive Design**: Optimal di semua perangkat
- **Toast Notification**: Feedback saat berhasil copy nomor rekening

### 🏦 **Bank yang Tersedia**
1. **Bank Central Asia (BCA)**
   - Logo: Official BCA SVG
   - Rekening: **********
   - Atas nama: <PERSON>

2. **Bank Mandiri**
   - Logo: Official Mandiri SVG
   - Rekening: **********
   - Atas nama: Siti Nurhaliza

3. **Bank Negara Indonesia (BNI)**
   - Logo: Official BNI SVG
   - Rekening: **********
   - Atas nama: Ahmad Rizki & Siti Nurhaliza

## 🎨 **Design Features**

### **Visual Elements:**
- **Gradient Background**: Blue light to cream
- **Card Design**: White cards with gold accent borders
- **Bank Logos**: 60x60px containers with official logos
- **Dropdown Icons**: Animated chevron with rotation
- **Copy <PERSON>tons**: Gradient blue-gold with hover effects

### **Animations:**
- **Dropdown**: Smooth height transition (0.4s ease)
- **Hover Effects**: Card lift and shadow enhancement
- **Copy Feedback**: Icon change to checkmark
- **Toast Notification**: Slide in from right

## 🔧 **Cara Mengubah Data Bank**

### **1. Mengganti Nomor Rekening**
Edit di `index.html`:
```html
<span class="account-digits">NOMOR-REKENING-BARU</span>
<button class="copy-btn" data-copy="NOMOR-REKENING-BARU">
```

### **2. Mengganti Nama Pemilik**
```html
<div class="bank-info">
    <h3>Nama Bank</h3>
    <p>a.n. NAMA-PEMILIK-BARU</p>
</div>
```

### **3. Mengganti Logo Bank**
```html
<div class="bank-logo">
    <img src="URL-LOGO-BANK-BARU" alt="Bank Logo">
</div>
```

### **4. Menambah Bank Baru**
Salin struktur gift-card dan ubah data:
```html
<div class="gift-card" data-bank="bank-baru">
    <div class="gift-header">
        <div class="bank-logo">
            <img src="logo-bank.svg" alt="Bank Logo">
        </div>
        <div class="bank-info">
            <h3>Nama Bank</h3>
            <p>a.n. Nama Pemilik</p>
        </div>
        <div class="dropdown-toggle">
            <i class="fas fa-chevron-down"></i>
        </div>
    </div>
    <div class="gift-content">
        <div class="account-number">
            <label>Nomor Rekening:</label>
            <div class="number-display">
                <span class="account-digits">**********</span>
                <button class="copy-btn" data-copy="**********">
                    <i class="fas fa-copy"></i>
                </button>
            </div>
        </div>
        <div class="account-name">
            <label>Atas Nama:</label>
            <span>Nama Pemilik</span>
        </div>
    </div>
</div>
```

## 🏦 **Sumber Logo Bank Indonesia**

### **Logo Resmi dari Wikipedia:**
```html
<!-- BCA -->
<img src="https://upload.wikimedia.org/wikipedia/commons/5/5c/Bank_Central_Asia.svg" alt="BCA">

<!-- Mandiri -->
<img src="https://upload.wikimedia.org/wikipedia/commons/a/ad/Bank_Mandiri_logo_2016.svg" alt="Mandiri">

<!-- BNI -->
<img src="https://upload.wikimedia.org/wikipedia/id/5/55/BNI_logo.svg" alt="BNI">

<!-- BRI -->
<img src="https://upload.wikimedia.org/wikipedia/commons/2/2e/BRI_2020.svg" alt="BRI">

<!-- CIMB Niaga -->
<img src="https://upload.wikimedia.org/wikipedia/commons/8/8a/CIMB_Niaga.svg" alt="CIMB">

<!-- Danamon -->
<img src="https://upload.wikimedia.org/wikipedia/commons/3/3e/Danamon-logo.svg" alt="Danamon">

<!-- Permata -->
<img src="https://upload.wikimedia.org/wikipedia/commons/thumb/2/25/Bank_Permata_logo.svg/320px-Bank_Permata_logo.svg.png" alt="Permata">
```

## 📱 **Responsive Behavior**

### **Desktop (> 768px):**
- Cards dalam layout vertikal
- Logo 60x60px
- Font size normal
- Horizontal layout untuk copy button

### **Tablet (≤ 768px):**
- Logo 50x50px
- Font size sedikit lebih kecil
- Padding yang disesuaikan

### **Mobile (≤ 480px):**
- Header menjadi column layout
- Logo 45x45px di center
- Copy button di bawah nomor rekening
- Font size optimal untuk mobile

## ⚡ **JavaScript Functionality**

### **Dropdown Toggle:**
```javascript
// Auto-close other cards when opening one
document.querySelectorAll('.gift-header').forEach(header => {
    header.addEventListener('click', function() {
        const giftCard = this.parentElement;
        const isActive = giftCard.classList.contains('active');
        
        // Close all other cards
        document.querySelectorAll('.gift-card').forEach(card => {
            card.classList.remove('active');
        });
        
        // Toggle current card
        if (!isActive) {
            giftCard.classList.add('active');
        }
    });
});
```

### **Copy to Clipboard:**
```javascript
// Modern clipboard API with fallback
navigator.clipboard.writeText(accountNumber).then(() => {
    // Success feedback
}).catch(err => {
    // Fallback for older browsers
    fallbackCopyTextToClipboard(accountNumber);
});
```

### **Toast Notification:**
- Appears top-right corner
- Auto-disappears after 3 seconds
- Slide in/out animation
- Shows copied account number

## 🎨 **Customization Options**

### **1. Mengubah Warna Tema**
```css
/* Ubah gradient background */
.wedding-gift-section {
    background: linear-gradient(135deg, YOUR-COLOR-1, YOUR-COLOR-2);
}

/* Ubah warna tombol copy */
.copy-btn {
    background: linear-gradient(135deg, YOUR-COLOR-1, YOUR-COLOR-2);
}
```

### **2. Mengubah Animasi**
```css
/* Ubah kecepatan dropdown */
.gift-content {
    transition: all 0.6s ease; /* dari 0.4s ke 0.6s */
}

/* Ubah efek hover */
.gift-card:hover {
    transform: translateY(-10px); /* dari -5px ke -10px */
}
```

### **3. Mengubah Layout**
```css
/* Layout horizontal untuk desktop */
.gift-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}
```

## 💡 **Tips & Best Practices**

### **1. Keamanan Data**
- Jangan gunakan nomor rekening asli untuk demo
- Ganti dengan nomor rekening yang benar sebelum publish
- Pastikan nama pemilik rekening sesuai

### **2. User Experience**
- Auto-open card pertama untuk guidance
- Provide clear feedback saat copy berhasil
- Gunakan font monospace untuk nomor rekening

### **3. Performance**
- Logo bank di-load dari CDN (Wikipedia)
- Animasi menggunakan CSS transform (hardware accelerated)
- Lazy loading untuk logo jika diperlukan

### **4. Accessibility**
- Proper ARIA labels untuk screen readers
- Keyboard navigation support
- High contrast colors

## 🔍 **Testing Checklist**

- [ ] Dropdown animation berfungsi smooth
- [ ] Copy button berhasil copy ke clipboard
- [ ] Toast notification muncul dan hilang
- [ ] Responsive di semua ukuran layar
- [ ] Logo bank ter-load dengan benar
- [ ] Navigation menu include "Gift"
- [ ] Auto-open first card saat page load
- [ ] Hover effects berfungsi dengan baik

## 🎉 **Hasil Akhir**

Wedding Gift section yang:
- ✅ **Interactive**: Dropdown animation yang smooth
- ✅ **Functional**: Copy to clipboard yang reliable
- ✅ **Beautiful**: Design yang elegant dengan logo resmi
- ✅ **Responsive**: Perfect di semua perangkat
- ✅ **User-Friendly**: Clear feedback dan easy to use
- ✅ **Professional**: Sesuai standar modern web development

---

**💳 Wedding Gift section siap digunakan untuk menerima kado digital!**
