# 🎨 Panduan Font Typography - Welcome Screen

## ✨ Font yang Digunakan

### 📝 Font Utama
1. **Great Vibes** - Untuk nama mempelai (elegant script)
2. **Playfair Display** - Untuk subtitle dan tombol (sophisticated serif)
3. **Poppins** - Untuk tanggal dan teks pendukung (modern sans-serif)
4. **Dancing Script** - Fallback untuk script font

### 🎯 Implementasi Font

#### **<PERSON><PERSON> (Welcome Title)**
```css
font-family: 'Great Vibes', 'Dancing Script', cursive;
font-size: 4.5rem;
font-weight: 400;
letter-spacing: 2px;
```
- **Great Vibes**: Font script yang sangat elegant dan romantic
- **Fallback**: Dancing Script jika Great Vibes tidak load
- **Ukuran**: 4.5rem untuk desktop, responsive untuk mobile

#### **Subtitle "Undangan Pernikahan"**
```css
font-family: 'Playfair Display', 'Poppins', serif;
font-size: 1.5rem;
font-weight: 500;
letter-spacing: 4px;
font-style: italic;
```
- **Playfair Display**: Font serif yang sophisticated
- **Style**: Italic untuk kesan elegant
- **Letter spacing**: 4px untuk kesan premium

#### **Tanggal Acara**
```css
font-family: 'Poppins', sans-serif;
font-size: 1.2rem;
font-weight: 500;
letter-spacing: 1px;
```
- **Poppins**: Font modern yang clean dan readable
- **Weight**: Medium (500) untuk emphasis

#### **Tombol "Buka Undangan"**
```css
font-family: 'Playfair Display', 'Poppins', serif;
font-size: 1.4rem;
font-weight: 600;
letter-spacing: 2px;
text-transform: capitalize;
```
- **Playfair Display**: Memberikan kesan premium pada tombol
- **Capitalize**: "Buka Undangan" (bukan BUKA UNDANGAN)
- **Letter spacing**: 2px untuk readability

## 📱 Responsive Typography

### 📱 Mobile (≤ 768px)
```css
.welcome-title { font-size: 3.2rem; }
.welcome-subtitle { font-size: 1.2rem; }
.welcome-date { font-size: 1rem; }
.btn-open-invitation { font-size: 1.2rem; }
```

### 📱 Small Mobile (≤ 480px)
```css
.welcome-title { font-size: 2.8rem; }
.welcome-subtitle { font-size: 1rem; }
.welcome-date { font-size: 0.9rem; }
.btn-open-invitation { font-size: 1.1rem; }
```

### 📱 Extra Small (≤ 320px)
```css
.welcome-title { font-size: 2.2rem; }
.welcome-subtitle { font-size: 0.9rem; }
.welcome-date { font-size: 0.8rem; }
.btn-open-invitation { font-size: 1rem; }
```

## 🎨 Efek Visual

### **Text Shadow**
- **Nama Mempelai**: `3px 3px 8px rgba(0, 0, 0, 0.5)`
- **Subtitle**: `2px 2px 4px rgba(0, 0, 0, 0.4)`
- **Tanggal**: `1px 1px 2px rgba(0, 0, 0, 0.3)`
- **Tombol**: `1px 1px 2px rgba(0, 0, 0, 0.3)`

### **Letter Spacing**
- **Nama**: 2px (elegant spacing)
- **Subtitle**: 4px (premium feel)
- **Tanggal**: 1px (readable)
- **Tombol**: 2px (clear text)

## 🔧 Cara Mengubah Font

### 1. **Mengganti Font Nama Mempelai**
Edit di `assets/css/style.css`:
```css
.welcome-title {
    font-family: 'Font-Baru', 'Great Vibes', cursive;
}
```

### 2. **Mengganti Font Subtitle**
```css
.welcome-subtitle {
    font-family: 'Font-Baru', 'Playfair Display', serif;
}
```

### 3. **Mengganti Font Tombol**
```css
.btn-open-invitation {
    font-family: 'Font-Baru', 'Playfair Display', sans-serif;
}
```

### 4. **Menambah Font Baru**
Tambahkan di `index.html`:
```html
<link href="https://fonts.googleapis.com/css2?family=Font-Baru:wght@400;500;600&display=swap" rel="stylesheet">
```

## 🎯 Rekomendasi Font Alternatif

### **Script Fonts (untuk nama)**
- **Allura**: Elegant script
- **Alex Brush**: Handwritten style
- **Pacifico**: Friendly script
- **Sacramento**: Clean script

### **Serif Fonts (untuk subtitle)**
- **Cormorant Garamond**: Classic elegance
- **Crimson Text**: Traditional serif
- **Libre Baskerville**: Modern serif
- **Lora**: Readable serif

### **Sans-Serif Fonts (untuk teks)**
- **Montserrat**: Modern geometric
- **Open Sans**: Friendly readable
- **Lato**: Humanist sans-serif
- **Nunito**: Rounded sans-serif

## 💡 Tips Typography

### **1. Kontras yang Baik**
- Font script untuk nama (artistic)
- Font serif untuk subtitle (elegant)
- Font sans-serif untuk info (readable)

### **2. Hierarchy yang Jelas**
- Nama: Paling besar dan mencolok
- Subtitle: Medium, elegant
- Tanggal: Kecil tapi jelas
- Tombol: Bold dan actionable

### **3. Readability**
- Letter spacing yang cukup
- Text shadow untuk kontras
- Font size yang responsive

### **4. Loading Performance**
- Gunakan `font-display: swap`
- Preload font penting
- Fallback font yang mirip

## 🎨 Kombinasi Warna Font

### **Current Color Scheme**
- **Nama**: Gold light (`var(--gold-light)`)
- **Subtitle**: Blue light (`var(--blue-light)`)
- **Tanggal**: White (`var(--white)`)
- **Tombol**: White (`var(--white)`)

### **Alternative Color Ideas**
```css
/* Elegant Gold Theme */
.welcome-title { color: #f4e4c1; }
.welcome-subtitle { color: #d4af37; }

/* Royal Blue Theme */
.welcome-title { color: #87ceeb; }
.welcome-subtitle { color: #4a90e2; }

/* Classic White Theme */
.welcome-title { color: #ffffff; }
.welcome-subtitle { color: #f0f0f0; }
```

## 🔍 Testing Font

### **Browser Testing**
1. Chrome/Edge: Ctrl+Shift+I → Elements
2. Firefox: F12 → Inspector
3. Safari: Cmd+Option+I → Elements

### **Font Loading Check**
```javascript
// Console command untuk check font
document.fonts.ready.then(() => {
    console.log('All fonts loaded!');
});
```

### **Responsive Testing**
- Desktop: 1920px, 1366px, 1024px
- Tablet: 768px, 834px
- Mobile: 375px, 414px, 320px

---

**🎉 Font typography yang elegant dan responsive siap digunakan!**
