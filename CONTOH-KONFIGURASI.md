# 📝 Contoh Konfigurasi Countdown Timer

## 🎯 Template Siap Pakai

Salin dan tempel salah satu konfigurasi di bawah ini ke file `assets/js/countdown-config.js`

---

## 💒 <PERSON>kad <PERSON>ah <PERSON>

```javascript
const COUNTDOWN_CONFIG = {
    date: '2025-07-15',
    time: '08:00',
    timezone: 'Asia/Jakarta',
    completedMessage: 'Akad Nikah Dimulai! 💒',
    runningMessage: 'Menuju Akad Nikah',
    enableCelebration: true,
    enableDebug: false,
    usePadding: true
};
```

---

## 🎉 Resepsi <PERSON>ang <PERSON>

```javascript
const COUNTDOWN_CONFIG = {
    date: '2025-07-15',
    time: '12:00',
    timezone: 'Asia/Jakarta',
    completedMessage: 'Resepsi Dimulai! 🎉',
    runningMessage: 'Menuju Resepsi Pernikahan',
    enableCelebration: true,
    enableDebug: false,
    usePadding: true
};
```

---

## 🌅 Resepsi Sore Hari

```javascript
const COUNTDOWN_CONFIG = {
    date: '2025-07-27',
    time: '16:00',
    timezone: 'Asia/Jakarta',
    completedMessage: 'Resepsi Sore Dimulai! 🌅',
    runningMessage: 'Menuju Resepsi Sore',
    enableCelebration: true,
    enableDebug: false,
    usePadding: true
};
```

---

## 🌙 Resepsi Malam Hari

```javascript
const COUNTDOWN_CONFIG = {
    date: '2025-07-15',
    time: '19:00',
    timezone: 'Asia/Jakarta',
    completedMessage: 'Resepsi Malam Dimulai! 🌙',
    runningMessage: 'Menuju Resepsi Malam',
    enableCelebration: true,
    enableDebug: false,
    usePadding: true
};
```

---

## 🎊 Acara Tahun Baru

```javascript
const COUNTDOWN_CONFIG = {
    date: '2025-12-31',
    time: '23:59',
    timezone: 'Asia/Jakarta',
    completedMessage: 'Selamat Tahun Baru! 🎊',
    runningMessage: 'Menuju Tahun Baru 2026',
    enableCelebration: true,
    enableDebug: false,
    usePadding: true
};
```

---

## 🎂 Ulang Tahun

```javascript
const COUNTDOWN_CONFIG = {
    date: '2025-08-17',
    time: '00:00',
    timezone: 'Asia/Jakarta',
    completedMessage: 'Selamat Ulang Tahun! 🎂',
    runningMessage: 'Menuju Hari Ulang Tahun',
    enableCelebration: true,
    enableDebug: false,
    usePadding: true
};
```

---

## 🎓 Wisuda

```javascript
const COUNTDOWN_CONFIG = {
    date: '2025-09-15',
    time: '09:00',
    timezone: 'Asia/Jakarta',
    completedMessage: 'Wisuda Dimulai! 🎓',
    runningMessage: 'Menuju Hari Wisuda',
    enableCelebration: true,
    enableDebug: false,
    usePadding: true
};
```

---

## 🏢 Acara Kantor

```javascript
const COUNTDOWN_CONFIG = {
    date: '2025-10-01',
    time: '14:00',
    timezone: 'Asia/Jakarta',
    completedMessage: 'Acara Dimulai! 🏢',
    runningMessage: 'Menuju Acara Perusahaan',
    enableCelebration: true,
    enableDebug: false,
    usePadding: true
};
```

---

## 🎪 Festival/Event

```javascript
const COUNTDOWN_CONFIG = {
    date: '2025-11-20',
    time: '18:00',
    timezone: 'Asia/Jakarta',
    completedMessage: 'Festival Dimulai! 🎪',
    runningMessage: 'Menuju Festival Musik',
    enableCelebration: true,
    enableDebug: false,
    usePadding: true
};
```

---

## ⚙️ Konfigurasi untuk Testing

### Test 5 Menit ke Depan
```javascript
const COUNTDOWN_CONFIG = {
    date: '2024-06-23',  // Ganti dengan tanggal hari ini
    time: '15:35',       // Ganti dengan 5 menit dari waktu sekarang
    timezone: 'Asia/Jakarta',
    completedMessage: 'Test Selesai! ✅',
    runningMessage: 'Testing Countdown',
    enableCelebration: true,
    enableDebug: true,   // Aktifkan untuk melihat log
    usePadding: true
};
```

### Test 1 Menit ke Depan
```javascript
const COUNTDOWN_CONFIG = {
    date: '2024-06-23',  // Ganti dengan tanggal hari ini
    time: '15:31',       // Ganti dengan 1 menit dari waktu sekarang
    timezone: 'Asia/Jakarta',
    completedMessage: 'Test Cepat Selesai! ⚡',
    runningMessage: 'Testing Countdown Cepat',
    enableCelebration: true,
    enableDebug: true,
    usePadding: true
};
```

---

## 🌍 Zona Waktu Indonesia

### WIB (Waktu Indonesia Barat)
```javascript
timezone: 'Asia/Jakarta',
// Meliputi: Jakarta, Bandung, Medan, Palembang, Pontianak
```

### WITA (Waktu Indonesia Tengah)
```javascript
timezone: 'Asia/Makassar',
// Meliputi: Makassar, Denpasar, Banjarmasin, Balikpapan
```

### WIT (Waktu Indonesia Timur)
```javascript
timezone: 'Asia/Jayapura',
// Meliputi: Jayapura, Ambon, Manokwari
```

---

## 💡 Tips Konfigurasi

### 1. Format Tanggal yang Benar
```javascript
// ✅ BENAR
date: '2025-01-15',  // 15 Januari 2025
date: '2025-06-23',  // 23 Juni 2025
date: '2025-12-31',  // 31 Desember 2025

// ❌ SALAH
date: '15-01-2025',  // Format salah
date: '2025/06/23',  // Gunakan dash (-), bukan slash (/)
date: '2025-6-23',   // Bulan harus 2 digit (06, bukan 6)
```

### 2. Format Waktu yang Benar
```javascript
// ✅ BENAR
time: '08:00',  // 8 pagi
time: '14:30',  // 2:30 siang
time: '19:45',  // 7:45 malam
time: '23:59',  // 11:59 malam

// ❌ SALAH
time: '8:00',   // Jam harus 2 digit (08, bukan 8)
time: '14:5',   // Menit harus 2 digit (05, bukan 5)
time: '2:30 PM', // Jangan gunakan AM/PM, gunakan format 24 jam
```

### 3. Pesan yang Menarik
```javascript
// Untuk pernikahan
completedMessage: 'Hari Bahagia Telah Tiba! 💕',
runningMessage: 'Menuju Hari Sakral Kami',

// Untuk ulang tahun
completedMessage: 'Selamat Ulang Tahun! 🎂🎉',
runningMessage: 'Menuju Hari Spesial',

// Untuk wisuda
completedMessage: 'Saatnya Wisuda! 🎓✨',
runningMessage: 'Menuju Hari Kelulusan',
```

---

**📝 Cara Menggunakan:**
1. Pilih template yang sesuai
2. Salin kode konfigurasi
3. Tempel ke file `assets/js/countdown-config.js`
4. Ubah tanggal dan waktu sesuai acara Anda
5. Simpan file dan refresh website

**🎉 Selamat! Countdown timer Anda siap digunakan!**
